import { HttpAgent } from "@ag-ui/client";
import {
  CopilotRuntime,
  copilotRuntimeNextJSAppRouterEndpoint,
  CopilotServiceAdapter,
  type CopilotRuntimeChatCompletionRequest,
  type CopilotRuntimeChatCompletionResponse,
} from "@copilotkit/runtime";
import { NextRequest } from "next/server";
import type { Logger } from "pino";

export interface CreateYaiNexusHandlerOptions {
  backendUrl: string;
  logger: Logger; // 必填：应用提供统一的 logger
  tracing?: {
    enabled?: boolean;
    generateTraceId?: () => string;
  };
}

/**
 * Lightweight adapter that proxies requests to AG-UI HttpAgent
 * Uses dependency injection for logger to integrate with application's unified logging system
 */
class YaiNexusServiceAdapter implements CopilotServiceAdapter {
  private httpAgent: HttpAgent;
  private options: CreateYaiNexusHandlerOptions;
  public baseLogger: Logger;

  constructor(backendUrl: string, options: CreateYaiNexusHandlerOptions) {
    // 使用 /agui 端点，这个端点返回 AG-UI 对象而不是 SSE 流
    const aguiUrl = backendUrl.endsWith("/")
      ? `${backendUrl}agui`
      : `${backendUrl}/agui`;

    this.httpAgent = new HttpAgent({
      url: aguiUrl,
      description: "YAI Nexus Agent for AG-UI protocol",
    });
    this.options = options;

    // 使用注入的 logger，添加 fekit 上下文
    this.baseLogger = options.logger.child({ component: "yai-nexus-fekit" });

    // 记录 HttpAgent 初始化信息
    this.baseLogger.info("HttpAgent initialized", {
      backendUrl,
      aguiUrl,
      httpAgentUrl: this.httpAgent.url,
    });
  }

  /**
   * 创建带有请求上下文的 logger
   */
  private createRequestLogger(context: {
    traceId?: string;
    runId?: string;
    threadId?: string;
  }): Logger {
    return this.baseLogger.child(context);
  }

  /**
   * 从事件数组中提取最终响应
   */
  private extractFinalResponse(events: any[], requestLogger: Logger): any {
    requestLogger.debug("Extracting final response from events", {
      eventCount: events.length,
      eventTypes: events.map((e) => e?.type),
    });

    // 查找最后一个包含内容的事件
    for (let i = events.length - 1; i >= 0; i--) {
      const event = events[i];
      if (event?.content || event?.data?.content) {
        requestLogger.debug("Found content event", {
          eventIndex: i,
          eventType: event.type,
          hasContent: !!event.content,
          hasDataContent: !!event.data?.content,
        });

        return {
          id: `response_${Date.now()}`,
          content:
            event.content ||
            event.data?.content ||
            "Response from YAI Nexus backend",
          role: "assistant",
        };
      }
    }

    // 如果没有找到内容事件，返回默认响应
    requestLogger.warn("No content event found, returning default response");
    return {
      id: `response_${Date.now()}`,
      content: "Response from YAI Nexus backend",
      role: "assistant",
    };
  }

  private generateTraceId(): string {
    if (this.options.tracing?.generateTraceId) {
      return this.options.tracing.generateTraceId();
    }
    // 默认生成策略：trace_ + 时间戳 + 随机数
    return `trace_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 处理非流式请求
   *
   * 注意：使用 any 类型是因为 CopilotRuntimeChatCompletionRequest 和
   * CopilotRuntimeChatCompletionResponse 类型没有从 @copilotkit/runtime 导出，
   * 但我们仍然正确实现了 CopilotServiceAdapter 接口
   */
  async process(
    request: CopilotRuntimeChatCompletionRequest
  ): Promise<CopilotRuntimeChatCompletionResponse> {
    // 生成或使用现有的追踪 ID
    const traceId = this.options.tracing?.enabled
      ? this.generateTraceId()
      : undefined;
    const threadId = request.threadId || traceId || "default";
    const runId = request.runId || `run_${Date.now()}`;

    // 格式化消息，确保每个消息都有 id 字段
    const formattedMessages = (request.messages || []).map(
      (msg: any, index: number) => ({
        id: msg.id || `msg_${Date.now()}_${index}`,
        role: msg.role,
        content: msg.content,
        ...(msg.name && { name: msg.name }),
        ...(msg.tool_calls && { tool_calls: msg.tool_calls }),
        ...(msg.tool_call_id && { tool_call_id: msg.tool_call_id }),
      })
    );

    // Since HttpAgent expects RunAgentInput format, we need minimal conversion
    const agentInput = {
      threadId,
      runId,
      messages: formattedMessages,
      tools: request.actions || [], // CopilotKit 使用 actions，但 HttpAgent 期望 tools
      context: [], // HttpAgent 需要，但 CopilotKit 没有提供
      state: null, // HttpAgent 需要，但 CopilotKit 没有提供
      forwardedProps: request.forwardedParameters || {},
    };

    // 创建请求级别的 logger
    const requestLogger = this.createRequestLogger({
      traceId,
      runId,
      threadId,
    });

    // 记录请求开始
    if (traceId) {
      requestLogger.info("Processing non-streaming request", {
        operation: "process",
        messageCount: request.messages?.length || 0,
      });
    }

    try {
      // 🎯 统一使用 run() 方法，保持架构一致性
      // 无论流式还是非流式，都使用相同的底层机制
      requestLogger.info("Calling HttpAgent.run (unified approach)", {
        method: "run",
        agentInput,
        httpAgentUrl: this.httpAgent.url,
      });

      const events$ = this.httpAgent.run(agentInput);

      requestLogger.info("HttpAgent.run returned observable", {
        observableType: typeof events$,
        hasSubscribe: typeof events$?.subscribe === "function",
      });

      // 手动收集 Observable 事件，避免 RxJS 版本冲突
      const events: any[] = [];

      return new Promise((resolve, reject) => {
        const subscription = events$.subscribe({
          next: (event: any) => {
            events.push(event);
            requestLogger.debug("Received event in process method", {
              eventType: event?.type,
              hasContent: !!event?.content,
              eventCount: events.length,
            });
          },
          error: (error: any) => {
            requestLogger.error("Observable error in process method", {
              error: error.message,
              stack: error.stack,
            });
            reject(error);
          },
          complete: () => {
            requestLogger.info("Observable completed in process method", {
              totalEvents: events.length,
              events: events.map((e) => ({
                type: e?.type,
                hasContent: !!e?.content,
              })),
            });

            // 从事件数组中提取最终响应
            const finalResponse = this.extractFinalResponse(
              events,
              requestLogger
            );

            requestLogger.info("Process method completed successfully", {
              eventCount: events.length,
              finalResponse: {
                id: finalResponse.id,
                role: finalResponse.role,
                hasContent: !!finalResponse.content,
              },
            });

            resolve(finalResponse);
          },
        });

        // 设置超时，防止无限等待
        setTimeout(() => {
          subscription.unsubscribe();
          if (events.length === 0) {
            requestLogger.warn("Observable timeout, no events received");
            reject(new Error("Observable timeout: no events received"));
          } else {
            requestLogger.warn("Observable timeout, using collected events", {
              eventCount: events.length,
            });
            const finalResponse = this.extractFinalResponse(
              events,
              requestLogger
            );
            resolve(finalResponse);
          }
        }, 30000); // 30秒超时
      });
    } catch (error) {
      requestLogger.error("Error in process method", {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      });

      throw error;
    }
  }
}

/**
 * Creates a Next.js API route handler that connects CopilotKit frontend
 * with yai-nexus-agentkit Python backend using AG-UI protocol
 *
 * @param options Configuration options for the handler
 * @returns Next.js POST handler function
 *
 * @example
 * ```typescript
 * // /src/app/api/copilotkit/route.ts
 * import { createYaiNexusHandler } from "@yai-nexus/fekit";
 * import { logger } from "@/lib/logger";
 *
 * export const POST = createYaiNexusHandler({
 *   backendUrl: process.env.PYTHON_BACKEND_URL!,
 *   logger, // 注入应用的统一 logger
 * });
 * ```
 */
export function createYaiNexusHandler(options: CreateYaiNexusHandlerOptions) {
  // Create lightweight service adapter that proxies to AG-UI HttpAgent
  const serviceAdapter = new YaiNexusServiceAdapter(
    options.backendUrl,
    options
  );

  // Create CopilotRuntime
  const runtime = new CopilotRuntime({
    middleware: {
      onBeforeRequest: async ({
        threadId,
        runId,
        inputMessages,
        properties,
      }) => {
        const logger = serviceAdapter.baseLogger.child({
          threadId,
          runId,
          phase: "before",
        });
        logger.info("CopilotRuntime request started", {
          messageCount: inputMessages.length,
          properties,
        });
      },
      onAfterRequest: async ({
        threadId,
        runId,
        inputMessages,
        outputMessages,
        properties,
      }) => {
        const logger = serviceAdapter.baseLogger.child({
          threadId,
          runId,
          phase: "after",
        });
        logger.info("CopilotRuntime request completed", {
          inputCount: inputMessages.length,
          outputCount: outputMessages.length,
          properties,
        });
      },
    },
  });

  // Create and return the Next.js POST handler
  const { handleRequest } = copilotRuntimeNextJSAppRouterEndpoint({
    runtime,
    serviceAdapter, // Use our lightweight adapter
    endpoint: "/api/copilotkit",
  });

  return async function POST(req: NextRequest) {
    try {
      return await handleRequest(req);
    } catch (error) {
      // 使用注入的 logger 记录错误
      serviceAdapter.baseLogger.error("Handler error", {
        error:
          error instanceof Error
            ? {
                name: error.name,
                message: error.message,
                stack: error.stack,
              }
            : { message: String(error) },
        url: req.url,
        method: req.method,
      });

      // Return a proper error response
      return new Response(
        JSON.stringify({
          error: "Internal server error",
          message: error instanceof Error ? error.message : "Unknown error",
        }),
        {
          status: 500,
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
    }
  };
}

/**
 * Type alias for the return type of createYaiNexusHandler
 */
export type YaiNexusHandler = ReturnType<typeof createYaiNexusHandler>;
