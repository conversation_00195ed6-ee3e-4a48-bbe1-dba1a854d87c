{"llms": [{"provider": "openai", "model": "gpt-4o", "api_key": "${OPENAI_API_KEY}", "base_url": "https://api.openai.com/v1"}, {"provider": "openai", "model": "gpt-3.5-turbo", "api_key": "${OPENAI_API_KEY}", "base_url": "https://api.openai.com/v1"}, {"provider": "openrouter", "model": "google/gemini-2.5-pro", "api_key": "${OPENROUTER_API_KEY}", "base_url": "https://openrouter.ai/api/v1"}, {"provider": "openrouter", "model": "anthropic/claude-sonnet-4", "api_key": "${OPENROUTER_API_KEY}", "base_url": "https://openrouter.ai/api/v1"}, {"provider": "tongyi", "model": "qwen-plus"}, {"provider": "do<PERSON>o", "model": "doubao-pro-4k", "api_key": "${DOUBAO_API_KEY}", "base_url": "https://ark.cn-beijing.volces.com/api/v3"}]}