{"name": "agentkit", "root": "packages/agentkit", "projectType": "library", "sourceRoot": "packages/agentkit/src", "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "uv pip install -e .", "cwd": "packages/agentkit"}}, "test": {"executor": "nx:run-commands", "options": {"command": "source ../../.venv/bin/activate && pytest tests/ -v", "cwd": "packages/agentkit"}}, "lint": {"executor": "nx:run-commands", "options": {"command": "source ../../.venv/bin/activate && ruff check .", "cwd": "packages/agentkit"}}, "format": {"executor": "nx:run-commands", "options": {"command": "source ../../.venv/bin/activate && black .", "cwd": "packages/agentkit"}}}, "tags": ["scope:backend", "type:lib", "lang:python"], "metadata": {"description": "Core AI agent toolkit with LLM factories, adapters, and persistence", "technologies": ["python", "<PERSON><PERSON><PERSON>", "langgraph", "tortoise-orm"], "maintainers": ["YAI-Nexus"]}}