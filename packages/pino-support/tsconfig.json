{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "declaration": true, "declarationMap": true, "sourceMap": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "noEmitOnError": true, "moduleResolution": "node", "target": "ES2020", "module": "ESNext", "lib": ["ES2020"]}, "include": ["src/**/*"], "exclude": ["dist", "node_modules", "**/*.test.ts", "**/*.spec.ts"]}