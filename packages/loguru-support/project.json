{"name": "loguru-support", "root": "packages/loguru-support", "projectType": "library", "sourceRoot": "packages/loguru-support/src", "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "uv pip install -e .", "cwd": "packages/loguru-support"}}, "test": {"executor": "nx:run-commands", "options": {"command": "source ../../.venv/bin/activate && pytest tests/ -v", "cwd": "packages/loguru-support"}}, "lint": {"executor": "nx:run-commands", "options": {"command": "source ../../.venv/bin/activate && ruff check .", "cwd": "packages/loguru-support"}}, "format": {"executor": "nx:run-commands", "options": {"command": "source ../../.venv/bin/activate && black .", "cwd": "packages/loguru-support"}}}, "tags": ["scope:logging", "type:lib", "lang:python"], "metadata": {"description": "Loguru sinks for integrating with cloud logging services", "technologies": ["python", "loguru", "aliyun-sls"], "maintainers": ["YAI-Nexus"]}}