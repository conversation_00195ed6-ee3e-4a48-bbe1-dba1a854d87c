# This workflow will upload Python Packages to PyPI when a release is created
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-python#publishing-to-package-registries

name: Upload Python Packages to PyPI

on:
  release:
    types: [published]

jobs:
  publish-agentkit:
    name: Publish yai-nexus-agentkit to PyPI
    if: startsWith(github.ref_name, 'agentkit-v')
    runs-on: ubuntu-latest
    permissions:
      id-token: write # This is required for trusted publishing
    environment:
      name: pypi
      url: https://pypi.org/p/yai-nexus-agentkit
    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install build dependencies
        run: python -m pip install build

      - name: Build release distributions
        working-directory: ./packages/agentkit
        run: python -m build

      - name: Publish to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          packages-dir: ./packages/agentkit/dist/

  publish-loguru-support:
    name: Publish yai-loguru-support to PyPI
    if: startsWith(github.ref_name, 'loguru-support-v')
    runs-on: ubuntu-latest
    permissions:
      id-token: write # This is required for trusted publishing
    environment:
      name: pypi
      url: https://pypi.org/p/yai-loguru-support
    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install build dependencies
        run: python -m pip install build

      - name: Build release distributions
        working-directory: ./packages/loguru-support
        run: python -m build

      - name: Publish to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          packages-dir: ./packages/loguru-support/dist/
