name: Publish All Packages

on:
  release:
    types: [published]

jobs:
  # Python Packages
  publish-agentkit:
    name: Publish yai-nexus-agentkit to PyPI
    if: startsWith(github.ref_name, 'v')
    runs-on: ubuntu-latest
    permissions:
      id-token: write # This is required for trusted publishing
    environment:
      name: pypi
      url: https://pypi.org/p/yai-nexus-agentkit
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"
      - name: Install build dependencies
        run: python -m pip install build
      - name: Build release distributions
        working-directory: ./packages/agentkit
        run: python -m build
      - name: Publish to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          packages-dir: ./packages/agentkit/dist/

  publish-loguru-support:
    name: Publish yai-loguru-support to PyPI
    if: startsWith(github.ref_name, 'v')
    runs-on: ubuntu-latest
    permissions:
      id-token: write # This is required for trusted publishing
    environment:
      name: pypi
      url: https://pypi.org/p/yai-loguru-support
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"
      - name: Install build dependencies
        run: python -m pip install build
      - name: Build release distributions
        working-directory: ./packages/loguru-support
        run: python -m build
      - name: Publish to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          packages-dir: ./packages/loguru-support/dist/

  # Node.js Packages
  publish-fekit:
    name: Publish @yai-nexus/fekit to NPM
    if: startsWith(github.ref_name, 'v')
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
        with:
          version: 9
      - uses: actions/setup-node@v4
        with:
          node-version: 20
          registry-url: 'https://registry.npmjs.org'
          cache: 'pnpm'
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      - name: Build package
        run: pnpm --filter @yai-nexus/fekit build
      - name: Publish to NPM
        working-directory: ./packages/fekit
        run: pnpm publish --no-git-checks
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

  publish-pino-support:
    name: Publish @yai-nexus/pino-support to NPM
    if: startsWith(github.ref_name, 'v')
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
        with:
          version: 9
      - uses: actions/setup-node@v4
        with:
          node-version: 20
          registry-url: 'https://registry.npmjs.org'
          cache: 'pnpm'
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      - name: Build package
        run: pnpm --filter @yai-nexus/pino-support build
      - name: Publish to NPM
        working-directory: ./packages/pino-support
        run: pnpm publish --no-git-checks
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }} 