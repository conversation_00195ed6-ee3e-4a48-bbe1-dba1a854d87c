name: Upload Node.js Packages to NPM

on:
  release:
    types: [published]

jobs:
  publish-fekit:
    name: Publish @yai-nexus/fekit to NPM
    if: startsWith(github.ref_name, 'fekit-v')
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
        with:
          version: 9
      - uses: actions/setup-node@v4
        with:
          node-version: 20
          registry-url: 'https://registry.npmjs.org'
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build package
        run: pnpm --filter @yai-nexus/fekit build

      - name: Publish to NPM
        working-directory: ./packages/fekit
        run: pnpm publish --no-git-checks
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

  publish-pino-support:
    name: Publish @yai-nexus/pino-support to NPM
    if: startsWith(github.ref_name, 'pino-support-v')
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
        with:
          version: 9
      - uses: actions/setup-node@v4
        with:
          node-version: 20
          registry-url: 'https://registry.npmjs.org'
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build package
        run: pnpm --filter @yai-nexus/pino-support build

      - name: Publish to NPM
        working-directory: ./packages/pino-support
        run: pnpm publish --no-git-checks
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
