{"name": "nextjs-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start -p 3000", "lint": "next lint"}, "dependencies": {"@ag-ui/client": "0.0.28", "@copilotkit/react-core": "^1.9.1", "@copilotkit/react-ui": "^1.9.1", "@yai-nexus/fekit": "file:../../packages/fekit", "@yai-nexus/pino-support": "file:../../packages/pino-support", "dotenv": "^16.4.5", "next": "15.3.5", "next-themes": "^0.4.6", "pino": "^9.5.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.0", "eslint": "^9", "eslint-config-next": "15.3.5", "pino-pretty": "^13.0.0", "tailwindcss": "^4", "typescript": "^5"}}