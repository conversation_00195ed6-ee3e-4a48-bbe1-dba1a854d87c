import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { ClientProviders } from "../components/ClientProviders";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "YAI Nexus FeKit Demo",
  description: "Demo app for yai-nexus-fekit SDK with CopilotKit integration",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ClientProviders>
          {children}
        </ClientProviders>
      </body>
    </html>
  );
}
