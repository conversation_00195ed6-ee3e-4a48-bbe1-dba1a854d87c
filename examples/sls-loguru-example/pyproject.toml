[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "yai-nexus-agentkit-sls-loguru-example"
version = "0.2.6"
description = "SLS (Simple Log Service) integration example using yai-loguru-support"
authors = [
    { name="YAI-Nexus", email="<EMAIL>" },
]
license = { file = "../../LICENSE" }
readme = "README.md"
requires-python = ">=3.9"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: System :: Logging",
]
dependencies = [
    "python-dotenv",
    "yai-loguru-support[sls]",
]

[project.urls]
Homepage = "https://github.com/yai-nexus/yai-nexus-agentkit"
Repository = "https://github.com/yai-nexus/yai-nexus-agentkit"

[project.optional-dependencies]
dev = [
    "pytest",
    "black",
    "ruff",
]

[tool.hatch.build.targets.wheel]
packages = ["."]