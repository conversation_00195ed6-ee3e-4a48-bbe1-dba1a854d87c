# SLS 日志集成示例 - 环境变量配置模板
# 复制此文件为 .env 并填入你的实际配置

# ===== 必填配置 =====

# SLS 服务端点 (根据你的阿里云区域选择)
# 常用区域端点:
# - 华东1(杭州): cn-hangzhou.log.aliyuncs.com
# - 华东2(上海): cn-shanghai.log.aliyuncs.com  
# - 华北2(北京): cn-beijing.log.aliyuncs.com
# - 华南1(深圳): cn-shenzhen.log.aliyuncs.com
SLS_ENDPOINT=cn-hangzhou.log.aliyuncs.com

# 阿里云 Access Key ID (在阿里云控制台 → AccessKey 管理 中获取)
SLS_AK_ID=your_access_key_id_here

# 阿里云 Access Key Secret (请妥善保管，不要提交到代码仓库)
SLS_AK_KEY=your_access_key_secret_here

# SLS 项目名称 (在阿里云 SLS 控制台中创建的项目)
SLS_PROJECT=your_log_project

# SLS 日志库名称 (在项目下创建的日志库)
SLS_LOGSTORE=app_logs

# ===== 可选配置 =====

# 日志主题 (用于日志分类，默认: python-app)
SLS_TOPIC=python-app

# 日志来源标识 (用于标识日志来源，默认: yai-loguru-support)  
SLS_SOURCE=sls-logger-example

# ===== 使用说明 =====
# 1. 复制此文件: cp .env.example .env
# 2. 编辑 .env 文件，填入你的实际配置
# 3. 运行示例: python main.py
#
# 注意: .env 文件已在 .gitignore 中，不会被提交到代码仓库