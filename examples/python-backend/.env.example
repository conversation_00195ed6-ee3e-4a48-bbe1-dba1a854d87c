# Python 后端环境配置示例
# 复制此文件为 .env 并根据需要修改配置

# =============================================================================
# 基础环境配置
# =============================================================================

# 环境类型：development | production | test
ENV=development

# 日志级别：DEBUG | INFO | WARN | ERROR
LOG_LEVEL=DEBUG

# 服务器配置
HOST=127.0.0.1
PORT=8000

# =============================================================================
# 日志配置
# =============================================================================

# 可选：日志文件路径（如果设置，将启用文件日志）
# LOG_FILE=logs/python-backend.log

# =============================================================================
# LLM 提供商配置
# =============================================================================

# OpenAI
# OPENAI_API_KEY=sk-...

# Anthropic  
# ANTHROPIC_API_KEY=sk-ant-...

# ZhipuAI
# ZHIPUAI_API_KEY=...

# 通义千问 (Dashscope)
# DASHSCOPE_API_KEY=sk-...

# OpenRouter
# OPENROUTER_API_KEY=sk-or-...

# =============================================================================
# 模型选择
# =============================================================================

# 指定要使用的模型（可选）
# MODEL_TO_USE=gpt-4o

# =============================================================================
# 生产环境配置（云日志服务）
# =============================================================================

# 阿里云 SLS 配置（仅生产环境需要）
# SLS_ENDPOINT=cn-hangzhou.log.aliyuncs.com
# SLS_AK_ID=your_access_key_id
# SLS_AK_KEY=your_access_key_secret
# SLS_PROJECT=your_log_project
# SLS_LOGSTORE=your_log_store