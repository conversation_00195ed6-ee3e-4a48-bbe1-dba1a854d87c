{"name": "yai-nexus-agentkit-monorepo", "version": "0.2.6", "private": true, "description": "Monorepo for yai-nexus-agentkit SDK and its example applications.", "scripts": {"dev:sdk": "pnpm --filter agentkit dev", "build:sdk": "pnpm --filter agentkit build", "dev:example:next": "NODE_OPTIONS='--inspect' pnpm --filter nextjs-app dev", "dev:example:python": "cd examples/python-backend && source ../../.venv/bin/activate && python main.py", "dev:example:sls-loguru": "cd examples/sls-loguru-example && source ../../.venv/bin/activate && python main.py", "dev:example:sls-pino": "pnpm --filter sls-pino-example dev", "install:python": "uv venv .venv && source .venv/bin/activate && uv pip install -r requirements.txt", "test:logging:python": "cd packages/loguru-support && source ../../.venv/bin/activate && python -m pytest tests/ -v", "test:logging:node": "pnpm --filter @yai-nexus/pino-support test", "test:logging": "pnpm test:logging:python && pnpm test:logging:node", "build:logging": "pnpm --filter @yai-nexus/loguru-support build && pnpm --filter @yai-nexus/pino-support build", "logs:run": "node scripts/cleanup-logs.js", "logs:cleanup": "node scripts/cleanup-logs.js --verbose", "logs:cleanup:dry": "node scripts/cleanup-logs.js --dry-run --verbose", "logs:cleanup:all": "node scripts/cleanup-logs.js --retention-days 0 --verbose", "logs:cleanup:all:dry": "node scripts/cleanup-logs.js --retention-days 0 --dry-run --verbose", "logs:stats": "node scripts/cleanup-logs.js --stats", "logs:cleanup:14d": "node scripts/cleanup-logs.js --retention-days 14 --verbose", "logs:cleanup:1gb": "node scripts/cleanup-logs.js --max-size 1GB --verbose", "examples:start": "./scripts/start-all-examples.sh", "examples:stop": "./scripts/stop-all-examples.sh", "clean:node_modules": "find . -name 'node_modules' -type d -prune -exec rm -rf '{}' +", "clean:node_modules:dry": "find . -name 'node_modules' -type d -prune -print"}, "workspaces": ["packages/*", "examples/*"], "author": "YAI Nexus Team", "license": "MIT", "devDependencies": {"@nx/devkit": "^21.2.3", "@nx/eslint": "^21.2.3", "@nx/js": "^21.2.3", "@nx/workspace": "^21.2.3", "nx": "^21.2.3"}}