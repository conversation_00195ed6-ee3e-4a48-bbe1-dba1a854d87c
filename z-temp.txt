Error: A tree hydrated but some attributes of the server rendered HTML didn't match the client properties. This won't be patched up. This can happen if a SSR-ed Client Component used:

- A server/client branch `if (typeof window !== 'undefined')`.
- Variable input such as `Date.now()` or `Math.random()` which changes each time it's called.
- Date formatting in a user's locale which doesn't match the server.
- External changing data without sending a snapshot of it along with the HTML.
- Invalid HTML tag nesting.

It can also happen if the client has a browser extension installed which messes with the HTML before React loaded.

https://react.dev/link/hydration-mismatch

  ...
    <CopilotKitInternal runtimeUrl="/api/copil...">
      <CopilotMessages>
        <YaiNexusPersistenceProvider userId="demo_user_..." conversationId="default-chat">
          <PersistenceSync userId="demo_user_..." conversationId="default-chat" autoSave={true} dbName={undefined}>
            <CopilotChat labels={{title:"YAI...", ...}}>
              <WrappedCopilotChat icons={undefined} labels={{title:"YAI...", ...}} className={undefined}>
                <ChatContextProvider icons={undefined} labels={{title:"YAI...", ...}} open={true} ...>
                  <div className={"copilotK..."}>
                    <Messages>
                    <Input inProgress={false} onSend={function handleSendMessage} isVisible={true} ...>
                      <div className="copilotKit...">
                        <div>
                        <PoweredByTag showPoweredBy={true}>
                          <div>
                            <p
                              className="poweredBy"
                              style={{
+                               visibility: "visible"
-                               visibility: "visible"
+                               display: "block"
-                               display: "block"
+                               position: "static"
-                               position: "static"
+                               textAlign: "center"
+                               fontSize: "12px"
+                               padding: "3px 0"
+                               color: "rgb(69, 69, 69)"
-                               color: "rgb(214, 214, 214)"
-                               text-align: "center"
-                               font-size: "12px"
-                               padding-top: "3px"
-                               padding-right: "0px"
-                               padding-bottom: "3px"
-                               padding-left: "0px"
                              }}
                            >
+                             Powered by CopilotKit

    at createConsoleError (webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@playwright+test@1.54.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/errors/console-error.js:27:71)
    at handleConsoleError (webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@playwright+test@1.54.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/errors/use-error-handler.js:47:54)
    at console.error (webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@playwright+test@1.54.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/globals/intercept-console-error.js:47:57)
    at eval (webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@playwright+test@1.54.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:4626:19)
    at runWithFiberInDEV (webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@playwright+test@1.54.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:845:30)
    at emitPendingHydrationWarnings (webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@playwright+test@1.54.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:4625:9)
    at completeWork (webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@playwright+test@1.54.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:11257:18)
    at runWithFiberInDEV (webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@playwright+test@1.54.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:848:13)
    at completeUnitOfWork (webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@playwright+test@1.54.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:15394:19)
    at performUnitOfWork (webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@playwright+test@1.54.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:15275:11)
    at workLoopConcurrentByScheduler (webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@playwright+test@1.54.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:15252:9)
    at renderRootConcurrent (webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@playwright+test@1.54.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:15227:15)
    at performWorkOnRoot (webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@playwright+test@1.54.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14525:13)
    at performWorkOnRootViaSchedulerTask (webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@playwright+test@1.54.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:16350:7)
    at MessagePort.performWorkUntilDeadline (webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@playwright+test@1.54.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/scheduler/cjs/scheduler.development.js:45:48)
    at p (<anonymous>)
    at PoweredByTag (webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/@copilotkit+react-ui@1.9.1_@types+react@19.1.8_graphql@16.11.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@copilotkit/react-ui/dist/chunk-CGEAG65D.mjs:25:170)
    at Input (webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/@copilotkit+react-ui@1.9.1_@types+react@19.1.8_graphql@16.11.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@copilotkit/react-ui/dist/chunk-EMIYIMQ6.mjs:106:75)
    at CopilotChat (webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/@copilotkit+react-ui@1.9.1_@types+react@19.1.8_graphql@16.11.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@copilotkit/react-ui/dist/chunk-RT4HE74K.mjs:250:75)
    at Home (webpack-internal:///(app-pages-browser)/./src/app/page.tsx:90:126)
    at ClientPageRoot (webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@playwright+test@1.54.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-page.js:20:50)