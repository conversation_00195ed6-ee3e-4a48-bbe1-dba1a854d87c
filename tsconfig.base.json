{"compilerOptions": {"target": "ES2020", "module": "ESNext", "lib": ["dom", "dom.iterable", "esnext"], "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "declaration": true, "declarationMap": true, "sourceMap": true, "allowJs": true, "baseUrl": ".", "paths": {"@yai-nexus/fekit": ["packages/fekit/src/index.ts"], "@yai-nexus/fekit/*": ["packages/fekit/src/*"], "@yai-nexus/pino-support": ["packages/pino-support/src/index.ts"], "@yai-nexus/pino-support/*": ["packages/pino-support/src/*"]}}, "exclude": ["node_modules", ".venv", "**/__pycache__", "**/*.pyc", "logs"]}