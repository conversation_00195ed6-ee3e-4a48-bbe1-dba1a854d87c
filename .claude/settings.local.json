{"permissions": {"allow": ["<PERSON><PERSON>(python:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(PRE_COMMIT_ALLOW_NO_CONFIG=1 git commit -m \"$(cat <<''EOF''\nfeat: 增强 LLM 模块功能，添加多模型支持和工厂模式\n\n- 添加模型枚举类 (OpenAIModel, AnthropicModel, ZhipuModel, TongyiModel, OpenRouterModel)\n- 实现 LLMFactory 类用于管理多个 LLM 实例\n- 更新 __init__.py 导出新的类和枚举\n- 完善 CLAUDE.md 文档，添加使用示例\n- 移除 .pre-commit-config.yaml 配置文件\n- 添加 LLM 使用演示脚本和技术设计文档\n- 更新 Claude 设置配置\nEOF\n)\")", "Bash(PRE_COMMIT_ALLOW_NO_CONFIG=1 git commit -m \"$(cat <<''EOF''\nupdate: 更新 Claude 设置配置权限\nEOF\n)\")", "Bash(black:*)", "Bash(ruff check:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(git status:*)", "Bash(git log:*)", "Bash(git diff:*)", "Bash(git push:*)", "Bash(cd:*)", "Bash(pip install:*)", "Bash(python -m examples.fast_api_app.main:*)", "<PERSON><PERSON>(uvicorn:*)", "Bash(ls:*)", "Bash(cp:*)", "Bash(find:*)", "WebFetch(domain:github.com)", "WebFetch(domain:langchain-ai.github.io)", "WebFetch(domain:docs.copilotkit.ai)", "WebFetch(domain:www.npmjs.com)", "Bash(pytest:*)", "<PERSON><PERSON>(git mv:*)", "Bash(git remote add:*)", "Bash(git fetch:*)", "Bash(git merge:*)", "Bash(npm run build:*)", "Bash(npm install:*)", "Bash(rm:*)", "Bash(npm view:*)", "Bash(grep:*)", "Bash(npm run dev:*)", "Bash(pnpm:*)", "Bash(git push:*)", "Bash(git remote remove:*)", "Bash(pnpm install:*)", "Bash(pnpm --filter @yai-nexus/fekit build)", "Bash(pnpm --filter nextjs-app build)", "Bash(cp:*)", "Bash(pip --version)", "<PERSON><PERSON>(source:*)", "<PERSON><PERSON>(timeout:*)", "Bash(node:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(touch:*)", "Bash(git tag:*)", "Bash(gh release create:*)", "<PERSON><PERSON>(true)", "Bash(OPENAI_API_KEY=\"\" python main.py)", "Bash(export OPENAI_API_KEY=\"test-key\")", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pip show:*)", "Bash(echo $VIRTUAL_ENV)", "WebFetch(domain:loguru.readthedocs.io)", "<PERSON><PERSON>(uv:*)", "<PERSON><PERSON>(time:*)", "Bash(npx nx init:*)", "Bash(npx nx show:*)", "Bash(npx nx graph:*)", "Bash(npx nx run-many:*)", "Bash(npx nx lint:*)", "Bash(kill:*)", "Bash(npm test)", "Bash(npm test:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./scripts/start-all-examples.sh:*)"], "deny": []}}