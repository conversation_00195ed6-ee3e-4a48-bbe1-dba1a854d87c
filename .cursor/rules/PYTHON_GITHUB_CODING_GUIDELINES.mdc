---
globs: packages/agentkit/**,examples/python-backend/**,examples/sls-loguru-example/**
alwaysApply: false
---
# Python 开源项目开发规则

## 1. 代码风格与质量

- **代码格式化**:
  - **工具**: `black`
  - **规范**: 所有 Python 代码必须使用 `black` 进行格式化。这消除了所有关于代码风格的争论，确保风格一致性。
  - **执行**: 通过 `pre-commit` 钩子在每次提交时自动执行。

- **代码静态检查 (Linting)**:
  - **工具**: `ruff`
  - **规范**: 使用 `ruff` 进行高效的静态代码分析，它整合了 `flake8`, `isort`, `pyupgrade` 等多种工具的功能。`ruff` 能帮助发现潜在的 bug、不规范的写法，并自动修复许多问题（包括导入排序）。
  - **执行**: 通过 `pre-commit` 钩子在每次提交时自动执行。

- **类型提示**:
  - **规范**: 始终使用类型提示 (Type Hints)，特别是在函数参数和返回值上。
  - **检查**: 可使用 `mypy` 或 `ruff` 内置的类型检查功能进行静态分析（可在 CI 中加入）。

- **代码可读性**:
  - 始终使用中文注释和文档字符串。
  - 遵循单一职责原则，保持函数和类的功能专注。
  - 使用 f-string 进行字符串格式化。

## 2. 开发工具与依赖

- **核心依赖**:
  - 日志工具: `loguru` + `loguru-support`
  - 配置工具: `yai-nexus-configuration`
  - LLM 访问层: `langchain`
  - AI Agents & Workflows: `langgraph 0.5.x`

- **开发与品控工具**:
  - 格式化: `black`
  - 静态检查: `ruff`
  - 自动化钩子: `pre-commit`
  - 测试框架: `pytest`
  - 测试覆盖率: `pytest-cov`

- **依赖管理**:
  - **工具**: `pip` 与 `pyproject.toml`。
  - **规范**: 在 `[project.dependencies]` 中定义核心依赖。在 `[project.optional-dependencies]` 中按功能（如 `openai`, `postgres`）对可选依赖进行分组。

## 3. 开发流程与自动化

- **提交前钩子 (Pre-commit Hooks)**:
  - **框架**: `pre-commit`
  - **目的**: 在代码提交（`git commit`）前，自动运行 `black` 和 `ruff` 等代码质量工具，确保所有进入代码库的代码都符合规范。
  - **使用**:
    1.  首次设置: 开发者克隆项目后，需运行一次 `pip install pre-commit && pre-commit install` 来安装钩子。
    2.  日常使用: 正常使用 `git commit`。如果代码不符合规范，提交将被阻止，并提示错误。按提示修改后或让工具自动修复后，重新暂存 (`git add`) 并提交即可。

## 4. 测试策略

- **分层测试**: 单元测试 + 集成测试 + 示例测试。
- **单元测试**: 独立且可重复。
- **集成测试**: 不能使用 Mock。需记录日志到 `logs` 目录。
- **示例测试**: 不能使用 Mock。需记录日志到 `logs` 目录。
- **测试覆盖率**:
  - **工具**: `pytest-cov`
  - **目标**: 整体测试覆盖率应保持在 **85%** 以上。CI/CD 流水线会检查此指标。
- **命名规范**:
  - 文件: `test_*.py`
  - 函数: `test_具体功能描述`
- **Fixtures**:
  - 临时文件/目录: 使用 `tmp_path` fixture。
  - 标准输出捕获: 使用 `capsys` fixture。

## 5. 错误处理

- **自定义基础异常**:
  - **规范**: 项目应定义一个基础异常类，例如 `AgentKitError(Exception)`。所有库中自定义的、可预见的异常都应继承自这个基础异常。
  - **目的**: 方便库的使用者通过 `except AgentKitError:` 来捕获所有来自本库的异常。
- **错误信息**: 提供清晰的错误消息，并在日志中包含足够的上下文信息。

## 6. 示例和演示

- **位置**: 在 `examples/` 目录提供完整的使用示例。
- **内容**: 示例应展示主要功能的用法，并提供与流行框架（如 FastAPI）的集成示例。
- **可运行性**:
  - **规范**: 确保示例代码可以独立运行。对于 Web 服务类示例（如 FastAPI），应提供一个 `if __name__ == '__main__':` 入口，通过编程方式启动服务（如 `uvicorn.run()`），使用户可以通过 `python a_script.py` 直接运行，而不是依赖外部命令行工具。
- **日志**: 示例中需要记录日志文件到 `logs` 目录。

## 7. 项目结构

- 采用标准的 Python 项目布局：

  ``` text
  项目根目录/
  ├── src/包名/          # 源代码
  │   ├── __init__.py    # 包初始化，导出公共 API
  │   ├── internal/      # 内部实现模块
  │   └── *.py           # 主要模块
  ├── tests/             # 测试代码
  │   ├── unit/          # 单元测试
  │   ├── integration/   # 集成测试
  │   └── examples/      # 示例测试
  ├── examples/          # 使用示例
  ├── logs/              # 日志目录
  ├── pyproject.toml     # 项目配置
  └── README.md          # 项目文档
  ```

## 8. 其他

### 设计模式

- 优先使用流式 API
- 使用单例模式管理全局状态(如 trace_context)
- 将内部实现放在 `internal/` 目录下，保持 API 简洁
- 使用工厂函数创建复杂对象

### 配置管理

- 使用 `pyproject.toml` 作为主要配置文件
- 在 `[project]` 中定义项目元数据
- 在 `[project.optional-dependencies]` 中定义开发依赖
- 配置 pytest 路径：`pythonpath = [".", "src"]`

### API 设计

- 提供简洁明了的公共 API
- 在 `__init__.py` 中明确导出公共接口
- 使用链式调用提升用户体验
- 提供合理的默认值
- 支持上下文管理器(如有需要)
