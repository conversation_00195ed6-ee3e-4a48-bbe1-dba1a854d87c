---
alwaysApply: false
---

# Python 代码审查指南

## 核心理念

代码审查 (Code Review) 的首要目标是**提升代码质量、促进知识共享、保障系统稳定性**。审查过程应被视为一个积极的、协作的、以学习为导向的活动，而非批判性的指责。我们的目标是共同写出更好的代码，而不是证明谁对谁错。

## 审查清单

### 1. 架构与设计 (Architecture & Design)

这是代码审查中最重要、最有价值的部分。好的设计能让系统更易于维护、扩展和理解。

- **[ ] 识别冗余设计**:
    - 是否存在不必要的抽象层、类或模块？
    - 代码是否遵循了 "你不需要它" (YAGNI, You Ain't Gonna Need It) 原则，避免了过度工程化？
    - 是否有多个模块/函数在做类似的事情？能否进行合并与抽象？

- **[ ] 拒绝“重新发明轮子”**:
    - **（重点）是否存在成熟的开源库可以替代自研实现？**
    - 例如：
        - HTTP 请求 → `requests`, `httpx`
        - 数据处理与分析 → `pandas`, `polars`
        - 日期时间操作 → `pendulum`, `arrow`
        - 命令行接口 → `click`, `typer`
        - 配置管理 → `pydantic-settings`
    - 在选择开源库时，请考虑其社区活跃度、文档质量、维护状态和许可证。

- **[ ] 单一职责原则 (Single Responsibility Principle, SRP)**:
    - 一个函数/类/模块是否只做一件事情？
    - **（重点）文件长度是否过长（例如，超过 300-500 行）？** 长文件通常意味着它承担了过多的职责，应考虑将其拆分为更小、更专注的模块。

- **[ ] 依赖倒置原则 (Dependency Inversion Principle, DIP)**:
    - 代码是否依赖于具体的实现而不是抽象？使用接口或抽象基类 (ABCs) 可以让代码更灵活。

- **[ ] 代码的可扩展性**:
    - 如果未来需求变更，当前的设计是否容易修改和扩展？例如，添加新的策略、支持新的数据格式等。

### 2. 功能与逻辑 (Functionality & Logic)

- **[ ] 代码是否正确实现了需求？**
    - 是否覆盖了所有的业务场景和边界条件？
    - 逻辑是否清晰、直接，没有不必要的复杂性？

- **[ ] 错误处理**:
    - 是否对可能发生的错误（如 I/O 错误、API 调用失败、数据格式错误）进行了妥善处理？
    - 是捕获了过于宽泛的异常（如 `except Exception:`）还是具体的异常类型？
    - 错误信息是否清晰，是否提供了足够的上下文用于调试？

- **[ ] 并发与异步**:
    - 如果使用了多线程、多进程或异步 I/O，是否存在竞态条件 (Race Conditions)、死锁 (Deadlocks) 或资源泄漏？
    - 异步代码是否正确使用了 `await`？`async def` 函数中是否有阻塞操作？

### 3. 可读性与代码风格 (Readability & Style)

可读的代码更容易维护。

- **[ ] PEP 8 规范**:
    - 代码是否遵循了 [PEP 8](https://www.python.org/dev/peps/pep-0008/) 风格指南？可以使用 `black`, `ruff format` 等工具自动格式化。

- **[ ] 命名**:
    - 变量、函数、类的命名是否清晰、有描述性，且能反映其用途？
    - 避免使用单字母变量名（循环变量 `i`, `j`, `k` 除外）和模糊的缩写。

- **[ ] 文档字符串 (Docstrings) 与注释**:
    - 公共的模块、类、函数和方法是否有符合 [PEP 257](https://www.python.org/dev/peps/pep-0257/) 规范的文档字符串？
    - 注释是否解释了代码的“为什么”而不是“做什么”？复杂的业务逻辑、算法或临时的解决方案应有注释说明。

- **[ ] 类型提示 (Type Hints)**:
    - 代码是否使用了类型提示（[PEP 484](https://www.python.org/dev/peps/pep-0484/)）？类型提示可以极大地提高代码的可读性和健壮性，并且是现代 Python 开发的最佳实践。

- **[ ] 保持简单 (KISS - Keep It Simple, Stupid)**:
    - 是否有更简单、更“Pythonic”的方式来实现相同的功能？
    - 避免不必要的复杂推导式和深层嵌套。

### 4. 性能与效率 (Performance & Efficiency)

- **[ ] 算法复杂度**:
    - 是否选择了合适的数据结构和算法？（例如，在需要快速查找的场景中，使用 `set` 或 `dict` 而不是 `list`）
    - 是否存在不必要的循环或嵌套循环，导致 O(n²) 或更高的复杂度？

- **[ ] 资源使用**:
    - 是否存在内存泄漏？文件、数据库连接等资源是否被正确关闭（推荐使用 `with` 语句）？
    - I/O 操作是否高效？例如，是否一次性读取了整个大文件到内存中？

### 5. 安全性 (Security)

- **[ ] 输入验证**:
    - 是否对所有外部输入（如用户请求、文件内容、环境变量）进行了严格的验证和清洗？

- **[ ] 注入风险**:
    - 如果代码与数据库或 Shell 交互，是否存在 SQL 注入或命令注入的风险？是否使用了参数化查询或安全的 API？

- **[ ] 敏感信息处理**:
    - 密码、API 密钥、Token 等敏感信息是否被硬编码在代码中？应使用环境变量或专门的密钥管理服务。
    - 日志中是否会意外记录敏感信息？

### 6. 测试 (Testing)

- **[ ] 测试覆盖率**:
    - 新增或修改的代码是否有相应的单元测试、集成测试？
    - 测试是否覆盖了核心逻辑、成功路径和常见的失败场景（边界条件）？

- **[ ] 测试的质量**:
    - 测试用例是否清晰、独立，且易于理解？
    - 是否对依赖的外部服务进行了模拟 (Mock)？

### 7. 依赖管理 (Dependency Management)

- **[ ] 依赖文件**:
    - 新增的依赖是否已添加到 `requirements.txt` 或 `pyproject.toml` 中？
    - 是否固定了依赖版本，以确保构建的可复现性？

## 审查与被审查的最佳实践

### 对于审查者 (Reviewer)

- **先理解后评论**: 在提出修改建议前，确保你已经理解了代码的意图和上下文。
- **提出建议，而非命令**: 使用“你觉得这样会不会更好？”而不是“你必须这么改。”
- **具体化建议**: 提供具体的代码示例来说明你的观点。
- **自动化检查**: 将风格、格式等问题交给 `ruff`, `black`, `mypy` 等自动化工具，让人专注于更高层次的问题。
- **赞美优点**: 对代码中优秀的设计和实现给予积极的肯定。

### 对于被审查者 (Author)

- **保持开放心态**: 将代码审查视为一个学习和提升的机会。
- **清晰解释**: 如果你的实现有特殊的背景或原因，请在代码或审查请求中清晰地说明。
- **及时响应**: 积极回应审查意见，进行讨论或修改。
- **小步快跑**: 提交小而专注的变更，这会让审查变得更容易、更快速。 