---
alwaysApply: false
---

# JavaScript/TypeScript 代码审查指南

## 核心理念

代码审查 (Code Review) 的首要目标是**提升代码质量、促进知识共享、保障系统稳定性**。审查过程应被视为一个积极的、协作的、以学习为导向的活动，而非批判性的指责。我们的目标是共同写出更好的代码，而不是证明谁对谁错。

## 审查清单

### 1. 架构与设计 (Architecture & Design)

这是代码审查中最重要、最有价值的部分。好的设计能让系统更易于维护、扩展和理解。

- **[ ] 识别冗余设计**:
    - 是否存在不必要的抽象、组件或模块？
    - 代码是否遵循了 "你不需要它" (YAGNI) 原则，避免了过度工程化？
    - 是否有多个组件/函数在做类似的事情？能否进行合并、抽象为可复用的 Hooks 或高阶组件/函数？

- **[ ] 拒绝“重新发明轮子”**:
    - **（重点）是否存在成熟的开源库可以替代自研实现？**
    - 例如：
        - HTTP 请求 → 内置 `fetch` API, `axios`
        - 日期时间操作 → `date-fns`, `day.js`
        - 复杂状态管理 → `zustand`, `jotai`, `redux-toolkit`
        - 表单处理 → `react-hook-form`, `formik`
        - 数据处理与工具函数 → `lodash`, `ramda`
    - 在选择开源库时，请考虑其 Bundle 大小、社区活跃度、文档质量、维护状态和 TypeScript 支持。

- **[ ] 单一职责原则 (Single Responsibility Principle, SRP)**:
    - 一个函数/组件/模块是否只做一件事情？
    - **（重点）文件长度是否过长（例如，超过 300-500 行）？** 长文件（尤其是组件文件）通常意味着它承担了过多的职责，应考虑将其拆分为更小、更专注的组件。

- **[ ] 组件设计与状态管理**:
    - 组件的 props 设计是否清晰、合理？
    - 状态的定义和管理是否恰当？是否将不必要的 state 提升到了过高的层级？
    - “受控”与“非受控”组件的选择是否合理？

- **[ ] 代码的可扩展性**:
    - 如果未来需求变更，当前的设计是否容易修改和扩展？例如，添加新的功能、支持新的数据源等。

### 2. 功能与逻辑 (Functionality & Logic)

- **[ ] 代码是否正确实现了需求？**
    - 是否覆盖了所有的业务场景和边界条件 (`null`, `undefined`, 空数组等)？
    - 逻辑是否清晰、直接，没有不必要的复杂性？

- **[ ] 异步处理**:
    - `Promise` 是否被正确处理？是否恰当地使用了 `async/await`？
    - 是否有完善的 `loading`, `error` 状态处理？
    - 是否考虑了竞态条件 (Race Conditions)，例如在组件卸载后 `Promise` 才 resolve 导致内存泄漏？可以考虑使用 `AbortController`。

- **[ ] 错误处理**:
    - 是否对可能发生的错误（如 API 调用失败、数据格式异常）进行了妥善处理？
    - `try...catch` 是否捕获了具体的错误？错误信息是否清晰，是否提供了足够的上下文用于调试？

- **[ ] TypeScript 特定审查点**:
    - **避免 `any`**: 是否滥用了 `any` 类型？应尽可能使用更具体的类型，或在必要时使用 `unknown`。
    - **类型定义**: 类型/接口的定义是否准确、清晰？是否利用了 `utility types` (`Partial`, `Pick`, `Omit` 等) 来减少重复代码？
    - **类型推断**: 是否充分利用了 TS 的类型推断，避免了不必要的类型注解？
    - **`!` 非空断言**: 是否过度使用了非空断言 (`!`)？这可能掩盖潜在的 `null` 或 `undefined` 错误。

### 3. 可读性与代码风格 (Readability & Style)

- **[ ] 格式化与 Linting**:
    - 代码是否遵循了团队的 `ESLint` 和 `Prettier` 规范？这些应该通过 CI/CD 强制执行。
    - 是否存在被注释掉的代码块？应及时清理。

- **[ ] 命名**:
    - 变量、函数、组件、类型的命名是否清晰、有描述性，且能反映其用途？（例如，布尔值使用 `is*`, `has*`, `should*` 开头）。
    - 避免使用单字母变量名和模糊的缩写。

- **[ ] 注释**:
    - 注释是否解释了代码的“为什么”而不是“做什么”？复杂的业务逻辑、算法或临时的 workarounds 应有注释说明。
    - 对于 TS，优先使用 JSDoc 风格的注释，以便在 IDE 中获得更好的提示。

- **[ ] 保持简单 (KISS)**:
    - 是否有更简单、更现代的语法来实现相同的功能？（例如 `optional chaining (?.)`, `nullish coalescing (??)`）
    - 避免不必要的深层嵌套和复杂的条件判断。

### 4. 性能与效率 (Performance & Efficiency)

- **[ ] Bundle Size 与 Tree Shaking**:
    - 引入的第三方库是否支持 Tree Shaking？是否只引入了需要的模块？
    - 是否有不必要的巨大依赖？可以使用 `bundle-analyzer` 等工具进行分析。

- **[ ] React/Frontend 特定审查点**:
    - **避免不必要的重渲染**: 是否过度使用了 `useState`？是否将 props 不变但依然重渲染的组件用 `React.memo` 包裹？
    - **昂贵的计算**: 是否用 `useMemo` 缓存了昂贵的计算结果？
    - **函数引用**: 是否用 `useCallback` 缓存了传递给子组件的函数，以避免不必要的渲染？
    - **巨大的列表**: 是否对长列表使用了虚拟化 (Virtualization) 技术？（如 `react-window`, `tanstack-virtual`）

- **[ ] Node.js/Backend 特定审查点**:
    - **事件循环**: 是否存在阻塞事件循环的同步操作？（如同步文件 I/O、复杂的 CPU 密集型计算）

### 5. 安全性 (Security)

- **[ ] 输入验证**:
    - 是否对所有外部输入（如用户请求、API 响应）进行了严格的验证？

- **[ ] 常见 Web 漏洞**:
    - **XSS (跨站脚本)**: 是否对用户输入的内容进行了正确的转义？在使用 `dangerouslySetInnerHTML` 或类似 API 时是否格外小心？
    - **CSRF (跨站请求伪造)**: 对于会产生副作用的请求（如 POST, PUT, DELETE），是否采取了 CSRF 保护措施？

- **[ ] 依赖安全**:
    - 是否定期运行 `npm audit` 或使用 `snyk` 等工具检查依赖库的安全漏洞？
    - 敏感信息（API Key, Token）是否被硬编码？应使用环境变量。

### 6. 测试 (Testing)

- **[ ] 测试覆盖率**:
    - 新增或修改的代码是否有相应的单元测试、集成测试或端到端测试？
    - 测试是否覆盖了核心逻辑、用户交互和常见的失败场景？

- **[ ] 测试的质量**:
    - 测试用例是否清晰、独立，且易于理解？
    - 是否优先测试用户行为，而不是组件的内部实现细节？（推荐使用 `@testing-library` 系列）

## 审查与被审查的最佳实践

### 对于审查者 (Reviewer)

- **先理解后评论**: 在提出修改建议前，确保你已经理解了代码的意图和上下文。
- **提出建议，而非命令**: 使用“你觉得这样会不会更好？”而不是“你必须这么改。”
- **具体化建议**: 提供具体的代码示例来说明你的观点。
- **自动化检查**: 将风格、格式等问题交给 `ESLint`, `Prettier`, `TypeScript` 编译器等自动化工具。
- **赞美优点**: 对代码中优秀的设计和实现给予积极的肯定。

### 对于被审查者 (Author)

- **保持开放心态**: 将代码审查视为一个学习和提升的机会。
- **清晰解释**: 如果你的实现有特殊的背景或原因，请在代码或审查请求中清晰地说明。
- **及时响应**: 积极回应审查意见，进行讨论或修改。
- **小步快跑**: 提交小而专注的变更（Small PRs），这会让审查变得更容易、更快速。 
---
# JavaScript/TypeScript 代码审查指南

## 核心理念

代码审查 (Code Review) 的首要目标是**提升代码质量、促进知识共享、保障系统稳定性**。审查过程应被视为一个积极的、协作的、以学习为导向的活动，而非批判性的指责。我们的目标是共同写出更好的代码，而不是证明谁对谁错。

## 审查清单

### 1. 架构与设计 (Architecture & Design)

这是代码审查中最重要、最有价值的部分。好的设计能让系统更易于维护、扩展和理解。

- **[ ] 识别冗余设计**:
    - 是否存在不必要的抽象、组件或模块？
    - 代码是否遵循了 "你不需要它" (YAGNI) 原则，避免了过度工程化？
    - 是否有多个组件/函数在做类似的事情？能否进行合并、抽象为可复用的 Hooks 或高阶组件/函数？

- **[ ] 拒绝“重新发明轮子”**:
    - **（重点）是否存在成熟的开源库可以替代自研实现？**
    - 例如：
        - HTTP 请求 → 内置 `fetch` API, `axios`
        - 日期时间操作 → `date-fns`, `day.js`
        - 复杂状态管理 → `zustand`, `jotai`, `redux-toolkit`
        - 表单处理 → `react-hook-form`, `formik`
        - 数据处理与工具函数 → `lodash`, `ramda`
    - 在选择开源库时，请考虑其 Bundle 大小、社区活跃度、文档质量、维护状态和 TypeScript 支持。

- **[ ] 单一职责原则 (Single Responsibility Principle, SRP)**:
    - 一个函数/组件/模块是否只做一件事情？
    - **（重点）文件长度是否过长（例如，超过 300-500 行）？** 长文件（尤其是组件文件）通常意味着它承担了过多的职责，应考虑将其拆分为更小、更专注的组件。

- **[ ] 组件设计与状态管理**:
    - 组件的 props 设计是否清晰、合理？
    - 状态的定义和管理是否恰当？是否将不必要的 state 提升到了过高的层级？
    - “受控”与“非受控”组件的选择是否合理？

- **[ ] 代码的可扩展性**:
    - 如果未来需求变更，当前的设计是否容易修改和扩展？例如，添加新的功能、支持新的数据源等。

### 2. 功能与逻辑 (Functionality & Logic)

- **[ ] 代码是否正确实现了需求？**
    - 是否覆盖了所有的业务场景和边界条件 (`null`, `undefined`, 空数组等)？
    - 逻辑是否清晰、直接，没有不必要的复杂性？

- **[ ] 异步处理**:
    - `Promise` 是否被正确处理？是否恰当地使用了 `async/await`？
    - 是否有完善的 `loading`, `error` 状态处理？
    - 是否考虑了竞态条件 (Race Conditions)，例如在组件卸载后 `Promise` 才 resolve 导致内存泄漏？可以考虑使用 `AbortController`。

- **[ ] 错误处理**:
    - 是否对可能发生的错误（如 API 调用失败、数据格式异常）进行了妥善处理？
    - `try...catch` 是否捕获了具体的错误？错误信息是否清晰，是否提供了足够的上下文用于调试？

- **[ ] TypeScript 特定审查点**:
    - **避免 `any`**: 是否滥用了 `any` 类型？应尽可能使用更具体的类型，或在必要时使用 `unknown`。
    - **类型定义**: 类型/接口的定义是否准确、清晰？是否利用了 `utility types` (`Partial`, `Pick`, `Omit` 等) 来减少重复代码？
    - **类型推断**: 是否充分利用了 TS 的类型推断，避免了不必要的类型注解？
    - **`!` 非空断言**: 是否过度使用了非空断言 (`!`)？这可能掩盖潜在的 `null` 或 `undefined` 错误。

### 3. 可读性与代码风格 (Readability & Style)

- **[ ] 格式化与 Linting**:
    - 代码是否遵循了团队的 `ESLint` 和 `Prettier` 规范？这些应该通过 CI/CD 强制执行。
    - 是否存在被注释掉的代码块？应及时清理。

- **[ ] 命名**:
    - 变量、函数、组件、类型的命名是否清晰、有描述性，且能反映其用途？（例如，布尔值使用 `is*`, `has*`, `should*` 开头）。
    - 避免使用单字母变量名和模糊的缩写。

- **[ ] 注释**:
    - 注释是否解释了代码的“为什么”而不是“做什么”？复杂的业务逻辑、算法或临时的 workarounds 应有注释说明。
    - 对于 TS，优先使用 JSDoc 风格的注释，以便在 IDE 中获得更好的提示。

- **[ ] 保持简单 (KISS)**:
    - 是否有更简单、更现代的语法来实现相同的功能？（例如 `optional chaining (?.)`, `nullish coalescing (??)`）
    - 避免不必要的深层嵌套和复杂的条件判断。

### 4. 性能与效率 (Performance & Efficiency)

- **[ ] Bundle Size 与 Tree Shaking**:
    - 引入的第三方库是否支持 Tree Shaking？是否只引入了需要的模块？
    - 是否有不必要的巨大依赖？可以使用 `bundle-analyzer` 等工具进行分析。

- **[ ] React/Frontend 特定审查点**:
    - **避免不必要的重渲染**: 是否过度使用了 `useState`？是否将 props 不变但依然重渲染的组件用 `React.memo` 包裹？
    - **昂贵的计算**: 是否用 `useMemo` 缓存了昂贵的计算结果？
    - **函数引用**: 是否用 `useCallback` 缓存了传递给子组件的函数，以避免不必要的渲染？
    - **巨大的列表**: 是否对长列表使用了虚拟化 (Virtualization) 技术？（如 `react-window`, `tanstack-virtual`）

- **[ ] Node.js/Backend 特定审查点**:
    - **事件循环**: 是否存在阻塞事件循环的同步操作？（如同步文件 I/O、复杂的 CPU 密集型计算）

### 5. 安全性 (Security)

- **[ ] 输入验证**:
    - 是否对所有外部输入（如用户请求、API 响应）进行了严格的验证？

- **[ ] 常见 Web 漏洞**:
    - **XSS (跨站脚本)**: 是否对用户输入的内容进行了正确的转义？在使用 `dangerouslySetInnerHTML` 或类似 API 时是否格外小心？
    - **CSRF (跨站请求伪造)**: 对于会产生副作用的请求（如 POST, PUT, DELETE），是否采取了 CSRF 保护措施？

- **[ ] 依赖安全**:
    - 是否定期运行 `npm audit` 或使用 `snyk` 等工具检查依赖库的安全漏洞？
    - 敏感信息（API Key, Token）是否被硬编码？应使用环境变量。

### 6. 测试 (Testing)

- **[ ] 测试覆盖率**:
    - 新增或修改的代码是否有相应的单元测试、集成测试或端到端测试？
    - 测试是否覆盖了核心逻辑、用户交互和常见的失败场景？

- **[ ] 测试的质量**:
    - 测试用例是否清晰、独立，且易于理解？
    - 是否优先测试用户行为，而不是组件的内部实现细节？（推荐使用 `@testing-library` 系列）

## 审查与被审查的最佳实践

### 对于审查者 (Reviewer)

- **先理解后评论**: 在提出修改建议前，确保你已经理解了代码的意图和上下文。
- **提出建议，而非命令**: 使用“你觉得这样会不会更好？”而不是“你必须这么改。”
- **具体化建议**: 提供具体的代码示例来说明你的观点。
- **自动化检查**: 将风格、格式等问题交给 `ESLint`, `Prettier`, `TypeScript` 编译器等自动化工具。
- **赞美优点**: 对代码中优秀的设计和实现给予积极的肯定。

### 对于被审查者 (Author)

- **保持开放心态**: 将代码审查视为一个学习和提升的机会。
- **清晰解释**: 如果你的实现有特殊的背景或原因，请在代码或审查请求中清晰地说明。
- **及时响应**: 积极回应审查意见，进行讨论或修改。
- **小步快跑**: 提交小而专注的变更（Small PRs），这会让审查变得更容易、更快速。 