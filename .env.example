# 统一日志系统配置示例
# 复制此文件为 .env 并根据需要修改配置

# =============================================================================
# 通用环境配置
# =============================================================================

# 环境类型：development | production | test
ENV=development

# 日志级别：DEBUG | INFO | WARN | ERROR
LOG_LEVEL=DEBUG

# =============================================================================
# Python 后端日志配置 (loguru)
# =============================================================================

# 可选：日志文件路径（如果设置，将启用文件日志）
# LOG_FILE=logs/application.log

# =============================================================================
# Next.js 应用配置
# =============================================================================

# Node.js 环境
NODE_ENV=development

# Python 后端地址
PYTHON_BACKEND_URL=http://127.0.0.1:8000/invoke

# =============================================================================
# SLS (阿里云日志服务) 配置 - 生产环境使用
# =============================================================================

# 阿里云 SLS 配置（仅生产环境需要）
# SLS_ENDPOINT=cn-hangzhou.log.aliyuncs.com
# SLS_AK_ID=your_access_key_id
# SLS_AK_KEY=your_access_key_secret
# SLS_PROJECT=your_log_project
# SLS_LOGSTORE=your_log_store

# =============================================================================
# LLM 提供商 API 密钥
# =============================================================================

# OpenAI
# OPENAI_API_KEY=sk-...

# Anthropic
# ANTHROPIC_API_KEY=sk-ant-...

# ZhipuAI
# ZHIPUAI_API_KEY=...

# 通义千问 (Dashscope)
# DASHSCOPE_API_KEY=sk-...

# OpenRouter
# OPENROUTER_API_KEY=sk-or-...

# =============================================================================
# 特定模型配置
# =============================================================================

# 指定要使用的模型（可选，覆盖配置文件中的设置）
# MODEL_TO_USE=gpt-4o